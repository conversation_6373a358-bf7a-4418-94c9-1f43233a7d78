#!/usr/bin/env python3
"""
Diagnose Extraction Issues

Simple test to understand why extractions are returning null.
"""

import os
import sys
import time
from pathlib import Path
from rich.console import Console

# Add project root to path
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

from ecommerce_scraper.tools.stagehand_tool import EcommerceStagehandTool

console = Console()

def main():
    """Diagnose extraction issues."""
    
    console.print("🔍 Diagnosing extraction issues...")
    
    url = "https://groceries.asda.com/aisle/fruit-veg-flowers/fruit/view-all-fruit/1215686352935-910000975210-1215666947025"
    
    try:
        with EcommerceStagehandTool.create_with_context() as tool:
            
            # Navigate
            console.print("📍 Navigating...")
            tool._run(
                instruction="Navigate to the ASDA fruit page",
                url=url,
                command_type="act"
            )
            time.sleep(5)
            
            # Simple page check
            console.print("📄 Basic page check...")
            page_check = tool._run(
                instruction="What do you see on this page? Describe the content.",
                command_type="extract"
            )
            console.print(f"Page check result: {page_check}")
            
            # Check for products specifically
            console.print("🛒 Looking for products...")
            product_check = tool._run(
                instruction="Are there any products visible on this page? If yes, describe a few.",
                command_type="extract"
            )
            console.print(f"Product check result: {product_check}")
            
            # Try different extraction approach
            console.print("📋 Alternative extraction...")
            alt_extraction = tool._run(
                instruction="List any items, products, or content you can see on this page.",
                command_type="extract"
            )
            console.print(f"Alternative extraction: {alt_extraction}")
            
            # Check if page loaded correctly
            console.print("🔍 Page load verification...")
            load_check = tool._run(
                instruction="Is this page fully loaded? Are there any loading indicators or errors?",
                command_type="extract"
            )
            console.print(f"Load check: {load_check}")
            
    except Exception as e:
        console.print(f"❌ Diagnosis failed: {str(e)}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    main()
