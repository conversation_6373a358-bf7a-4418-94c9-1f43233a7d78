#!/usr/bin/env python3
"""
Validate Scroll Fix

Direct test to confirm infinite scroll handling works for ASDA 122 products.
"""

import os
import sys
import time
from pathlib import Path
from rich.console import Console
from rich.panel import Panel

# Add project root to path
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

from ecommerce_scraper.tools.stagehand_tool import EcommerceStagehandTool

console = Console()

def main():
    """Direct validation of scroll fix."""
    
    console.print(Panel(
        "[bold blue]🔄 Scroll Fix Validation[/bold blue]\n\n"
        "Testing: Can we load all 122 ASDA fruit products?\n"
        "Method: Progressive scrolling with product counting",
        title="Scroll Validation"
    ))
    
    url = "https://groceries.asda.com/aisle/fruit-veg-flowers/fruit/view-all-fruit/1215686352935-910000975210-1215666947025"
    
    try:
        console.print("🚀 Starting Stagehand session...")
        with EcommerceStagehandTool.create_with_context() as tool:
            
            # Navigate
            console.print("📍 Navigating to ASDA fruit page...")
            tool._run(
                instruction="Navigate to the ASDA fruit page",
                url=url,
                command_type="act"
            )
            time.sleep(5)
            
            # Initial check
            console.print("📊 Initial product count check...")
            initial_info = tool._run(
                instruction="Look at this page and tell me: How many products are currently visible? Is there text showing 'Showing X of Y items'?",
                command_type="extract"
            )
            console.print(f"Initial: {initial_info[:200]}...")
            
            # Progressive scrolling
            console.print("\n🔄 Starting progressive scrolling...")
            for scroll_num in range(1, 6):
                console.print(f"\n--- Scroll {scroll_num} ---")
                
                # Scroll down
                console.print(f"Scrolling down (attempt {scroll_num})...")
                tool._run(
                    instruction="Scroll down the page to load more products",
                    command_type="act"
                )
                time.sleep(4)
                
                # Check products after scroll
                product_check = tool._run(
                    instruction=f"After scroll {scroll_num}: How many products are now visible? What does the 'Showing X of Y items' text say?",
                    command_type="extract"
                )
                console.print(f"After scroll {scroll_num}: {product_check[:150]}...")
            
            # Final extraction attempt
            console.print("\n📋 Final extraction attempt...")
            final_products = tool._run(
                instruction="Extract the first 20 fruit products currently visible. For each, provide: name and price. Number them 1-20.",
                command_type="extract"
            )
            
            # Count extracted products
            import re
            numbered_items = re.findall(r'^\s*\d+\.', final_products, re.MULTILINE)
            extracted_count = len(numbered_items)
            
            console.print(f"\n📊 Final Results:")
            console.print(f"Products extracted in sample: {extracted_count}/20")
            console.print(f"Sample extraction: {final_products[:300]}...")
            
            # Assessment
            if extracted_count >= 15:
                console.print(f"\n[bold green]✅ SCROLL FIX WORKING![/bold green]")
                console.print("The infinite scroll handling is functional.")
                console.print("Ready to integrate into main scraper.")
                return True
            elif extracted_count >= 10:
                console.print(f"\n[bold yellow]⚠️ PARTIAL SUCCESS[/bold yellow]")
                console.print("Scroll handling working but needs refinement.")
                return False
            else:
                console.print(f"\n[bold red]❌ SCROLL FIX NEEDS WORK[/bold red]")
                console.print("Infinite scroll handling not working properly.")
                return False
                
    except Exception as e:
        console.print(f"\n❌ Test failed with error: {str(e)}")
        import traceback
        traceback.print_exc()
        return False


if __name__ == "__main__":
    success = main()
    console.print(f"\nTest {'PASSED' if success else 'FAILED'}")
    sys.exit(0 if success else 1)
