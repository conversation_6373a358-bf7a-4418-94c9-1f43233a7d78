#!/usr/bin/env python3
"""
Simple 122 Products Test

A simplified test to confirm we can extract all 122 ASDA fruit products.
"""

import os
import sys
import json
import time
from pathlib import Path
from datetime import datetime
from rich.console import Console
from rich.panel import Panel

# Add project root to path
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

from ecommerce_scraper.tools.stagehand_tool import EcommerceStagehandTool

console = Console()

def test_asda_122_products():
    """Simple test to extract all 122 ASDA fruit products."""
    
    url = "https://groceries.asda.com/aisle/fruit-veg-flowers/fruit/view-all-fruit/1215686352935-910000975210-1215666947025"
    
    console.print(Panel(
        "[bold blue]🎯 Simple 122 Products Test[/bold blue]\n\n"
        "Goal: Extract all 122 fruit products from ASDA\n"
        "Method: Manual scrolling + targeted extraction",
        title="122 Products Test"
    ))
    
    try:
        with EcommerceStagehandTool.create_with_context() as tool:
            # Navigate to page
            console.print("📍 Navigating to ASDA fruit page...")
            tool._run(
                instruction="Navigate to the ASDA fruit page",
                url=url,
                command_type="act"
            )
            time.sleep(4)
            
            # Check initial state
            console.print("📊 Checking page status...")
            page_status = tool._run(
                instruction="Tell me: 1) How many products are visible? 2) Is there text showing 'Showing X of Y items'? 3) What's the total number of items mentioned?",
                command_type="extract"
            )
            console.print(f"Page status: {page_status}")
            
            # Scroll multiple times to load all products
            console.print("\n🔄 Scrolling to load all products...")
            for i in range(8):  # Try 8 scrolls
                console.print(f"Scroll {i+1}/8...")
                tool._run(
                    instruction="Scroll down the page slowly to load more products",
                    command_type="act"
                )
                time.sleep(3)
            
            # Check final state
            console.print("\n📊 Checking final state...")
            final_status = tool._run(
                instruction="Now tell me: 1) How many products are visible? 2) What does the 'Showing X of Y items' text say now? 3) Are all 122 items loaded?",
                command_type="extract"
            )
            console.print(f"Final status: {final_status}")
            
            # Extract all products
            console.print("\n📋 Extracting all visible products...")
            all_products = tool._run(
                instruction="Extract ALL fruit products currently visible on this page. For each product, provide: 1) Product name, 2) Price. Number each product (1., 2., 3., etc.). Focus only on actual fruit products.",
                command_type="extract"
            )
            
            # Count products in extraction
            product_count = count_products(all_products)
            console.print(f"\n📊 Products extracted: {product_count}")
            
            # Save results
            save_results(all_products, product_count, page_status, final_status)
            
            # Success assessment
            if product_count >= 110:  # 90% of 122
                console.print(f"\n[bold green]🎉 SUCCESS![/bold green] Extracted {product_count}/122 products")
                console.print("✅ Ready for integration into main scraper!")
                return True
            elif product_count >= 80:
                console.print(f"\n[bold yellow]⚠️ PARTIAL SUCCESS[/bold yellow] Extracted {product_count}/122 products")
                console.print("🔧 Needs some refinement")
                return False
            else:
                console.print(f"\n[bold red]❌ NEEDS WORK[/bold red] Only extracted {product_count}/122 products")
                console.print("🔧 Requires significant improvements")
                return False
                
    except Exception as e:
        console.print(f"❌ Test failed: {str(e)}")
        import traceback
        traceback.print_exc()
        return False


def count_products(extraction_text):
    """Count products in extraction text."""
    if not extraction_text:
        return 0
    
    # Count numbered items (1., 2., 3., etc.)
    import re
    numbered_items = re.findall(r'^\s*\d+\.', extraction_text, re.MULTILINE)
    if numbered_items:
        return len(numbered_items)
    
    # Fallback: count lines with price indicators
    lines = extraction_text.split('\n')
    price_lines = [line for line in lines if '£' in line or 'price' in line.lower()]
    return len(price_lines)


def save_results(products_data, count, initial_status, final_status):
    """Save test results."""
    try:
        results_dir = Path("test_results")
        results_dir.mkdir(exist_ok=True)
        
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        filename = f"simple_122_test_{timestamp}.json"
        filepath = results_dir / filename
        
        data = {
            "test_info": {
                "timestamp": timestamp,
                "target": 122,
                "extracted": count,
                "success_rate": (count / 122) * 100
            },
            "page_states": {
                "initial": initial_status,
                "final": final_status
            },
            "products_data": products_data[:5000]  # Truncate for file size
        }
        
        with open(filepath, 'w', encoding='utf-8') as f:
            json.dump(data, f, indent=2, ensure_ascii=False)
        
        console.print(f"📁 Results saved to: {filepath}")
        
    except Exception as e:
        console.print(f"⚠️ Failed to save results: {str(e)}")


def main():
    """Main execution."""
    success = test_asda_122_products()
    sys.exit(0 if success else 1)


if __name__ == "__main__":
    main()
